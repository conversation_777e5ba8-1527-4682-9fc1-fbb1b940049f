<!-- 业主管理页面 -->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索楼栋"
      bind:change="onSearchChange"
      bind:search="onSearchSubmit"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onSearchSubmit">搜索</view>
    </van-search>
  </view>

  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <van-loading type="spinner" size="24px">加载中...</van-loading>
    </view>

    <!-- 主体布局 -->
    <view class="content-layout" wx:else>
      <!-- 左侧楼栋列表 -->
      <view class="sidebar-container">
        <van-sidebar active-key="{{activeBuildingIndex}}" bind:change="onBuildingChange">
          <van-sidebar-item
            wx:for="{{filteredBuildings}}"
            wx:key="buildingId"
            title="{{item.name}}"
            info="{{item.houseCount}}套"
          />
        </van-sidebar>
        
        <!-- 楼栋为空提示 -->
        <view class="empty-buildings" wx:if="{{filteredBuildings.length === 0 && !loading}}">
          <van-empty description="暂无楼栋数据" />
        </view>
      </view>

      <!-- 右侧房屋列表 -->
      <view class="house-container">
        <!-- 房屋列表 -->
        <view class="house-list" wx:if="{{currentBuilding}}">
          <view class="building-header">
            <text class="building-name">{{currentBuilding.name}}</text>
            <text class="house-count">共{{houses.length}}套房屋</text>
          </view>

          <scroll-view
            class="house-scroll"
            scroll-y
            bindscrolltolower="loadMoreHouses"
            lower-threshold="100"
          >
            <van-cell-group>
              <van-cell
                wx:for="{{houses}}"
                wx:key="houseId"
                title="{{item.room}}"
                label="{{item.floor}}层 · {{item.useArea}}㎡"
                value="{{item.ownerCount > 0 ? item.ownerCount + '位业主' : '暂无业主'}}"
                is-link
                bind:click="onHouseClick"
                data-house="{{item}}"
              >
                <view slot="right-icon" class="house-status">
                  <van-tag
                    wx:if="{{item.houseStatus === '2001'}}"
                    type="success"
                    size="small"
                  >已入住</van-tag>
                  <van-tag
                    wx:elif="{{item.houseStatus === '2003'}}"
                    type="primary"
                    size="small"
                  >已交房</van-tag>
                  <van-tag
                    wx:elif="{{item.houseStatus === '2004'}}"
                    type="warning"
                    size="small"
                  >未入住</van-tag>
                  <van-tag
                    wx:else
                    type="default"
                    size="small"
                  >{{item.houseStatus || '未知'}}</van-tag>
                </view>
              </van-cell>
            </van-cell-group>

            <!-- 加载更多提示 -->
            <view class="load-more" wx:if="{{houses.length > 0}}">
              <view wx:if="{{houseLoading}}" class="loading-more">
                <van-loading type="spinner" size="16px">加载中...</van-loading>
              </view>
              <view wx:elif="{{!houseHasMore}}" class="no-more">
                <text>没有更多数据了</text>
              </view>
              <view wx:else class="pull-more">
                <text>上拉加载更多</text>
              </view>
            </view>

            <!-- 房屋为空提示 -->
            <view class="empty-houses" wx:if="{{houses.length === 0 && !houseLoading}}">
              <van-empty description="该楼栋暂无房屋" />
            </view>
          </scroll-view>
        </view>

        <!-- 未选择楼栋提示 -->
        <view class="no-building-selected" wx:else>
          <van-empty description="请选择楼栋查看房屋信息" />
        </view>

        <!-- 房屋加载状态 -->
        <view class="house-loading" wx:if="{{houseLoading}}">
          <van-loading type="spinner" size="20px">加载房屋中...</van-loading>
        </view>
      </view>
    </view>
  </view>
</view>
