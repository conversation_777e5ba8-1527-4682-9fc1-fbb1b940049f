/* 业主管理页面样式 */
.container {
  height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 搜索栏 */
.search-container {
  background: white;
  padding: 16rpx;
  border-bottom: 1px solid #ebedf0;
}

/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容布局 */
.content-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
  padding-right: 24rpx;
}

/* 左侧楼栋列表 */
.sidebar-container {
  width: 168rpx;
  background: white;
  border-right: 1px solid #ebedf0;
  overflow-y: auto;
}

.empty-buildings {
  padding: 60rpx 20rpx;
  text-align: center;
}

/* 右侧房屋列表 */
.house-container {
  flex: 1;
  background: #f7f8fa;
  overflow-y: auto;
  position: relative;
}

.building-header {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #ebedf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.building-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.house-count {
  font-size: 24rpx;
  color: #969799;
}

.house-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.house-scroll {
  flex: 1;
  height: 0;
}

.house-status {
  margin-left: 16rpx;
}

.load-more {
  padding: 32rpx;
  text-align: center;
}

.loading-more {
  color: #969799;
}

.no-more {
  color: #c8c9cc;
  font-size: 24rpx;
}

.pull-more {
  color: #969799;
  font-size: 24rpx;
}

.empty-houses,
.no-building-selected {
  padding: 120rpx 40rpx;
  text-align: center;
}

.house-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 房屋详情弹窗 */
.house-detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 32rpx;
  border-bottom: 1px solid #ebedf0;
  text-align: center;
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx 32rpx;
}

.detail-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-title text {
  margin-left: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.empty-owners {
  padding: 60rpx 20rpx;
  text-align: center;
}

.detail-loading {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义van-sidebar样式 */
.van-sidebar-item {
  font-size: 28rpx !important;
}

.van-sidebar-item__text {
  line-height: 1.4 !important;
}

/* 自定义van-cell样式 */
.van-cell__title {
  font-size: 32rpx !important;
  font-weight: 500 !important;
}

.van-cell__label {
  font-size: 24rpx !important;
  color: #969799 !important;
  margin-top: 8rpx !important;
}

.van-cell__value {
  font-size: 28rpx !important;
  color: #646566 !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .sidebar-container {
    width: 200rpx;
  }
  
  .building-header {
    padding: 20rpx 24rpx;
  }
  
  .building-name {
    font-size: 30rpx;
  }
}

@media (min-width: 414px) {
  .sidebar-container {
    width: 280rpx;
  }
}
