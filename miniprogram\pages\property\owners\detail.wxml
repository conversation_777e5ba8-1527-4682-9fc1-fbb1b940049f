<!-- 房屋详情页面 -->
<wxs module="utils">
  function getRelTypeText(type) {
    var typeMap = {
      '1': '业主',
      '2': '家庭成员',
      '3': '租户',
      '4': '其他'
    }
    return typeMap[type + ''] || '其他'
  }

  function getHouseTypeText(type) {
    var typeMap = {
      '1': '住宅',
      '2': '公寓',
      '3': '商铺',
      '4': '办公'
    }
    return typeMap[type + ''] || '未知类型'
  }

  function getParkingTypeText(type) {
    var typeMap = {
      '1': '地上车位',
      '2': '地下车位',
      '3': '机械车位',
      '4': '临时车位'
    }
    return typeMap[type + ''] || '普通车位'
  }

  function getVehicleTypeText(type) {
    var typeMap = {
      '1': '小型汽车',
      '2': '大型汽车',
      '3': '摩托车',
      '4': '电动车',
      '5': '其他'
    }
    return typeMap[type + ''] || '未知类型'
  }

  function formatTime(timeStr) {
    if (!timeStr) return '未知时间'

    try {
      var date = getDate(timeStr)
      var year = date.getFullYear()
      var month = (date.getMonth() + 1 + '').padStart(2, '0')
      var day = (date.getDate() + '').padStart(2, '0')
      return year + '-' + month + '-' + day
    } catch (error) {
      return timeStr
    }
  }

  module.exports = {
    getRelTypeText: getRelTypeText,
    getHouseTypeText: getHouseTypeText,
    getParkingTypeText: getParkingTypeText,
    getVehicleTypeText: getVehicleTypeText,
    formatTime: formatTime
  }
</wxs>

<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <scroll-view class="detail-content" scroll-y wx:else>
    <!-- 房屋基本信息 -->
    <view class="info-card">
      <view class="card-header">
        <van-icon name="home-o" size="20px" color="#1989fa" />
        <text class="card-title">房屋信息</text>
      </view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">房屋编号</text>
          <text class="info-value">{{houseDetail.houseInfo.room}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">楼栋</text>
          <text class="info-value">{{houseDetail.houseInfo.building_name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">单元</text>
          <text class="info-value">{{houseDetail.houseInfo.unit_name || '无'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">楼层</text>
          <text class="info-value">{{houseDetail.houseInfo.floor}}层</text>
        </view>
        <view class="info-item">
          <text class="info-label">使用面积</text>
          <text class="info-value">{{houseDetail.houseInfo.use_area}}㎡</text>
        </view>
        <view class="info-item">
          <text class="info-label">建筑面积</text>
          <text class="info-value">{{houseDetail.houseInfo.total_area}}㎡</text>
        </view>
        <view class="info-item">
          <text class="info-label">房屋类型</text>
          <text class="info-value">{{utils.getHouseTypeText(houseDetail.houseInfo.house_type)}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">房屋状态</text>
          <van-tag
            wx:if="{{houseDetail.houseInfo.house_status === '2001'}}"
            type="success"
            size="small"
          >已入住</van-tag>
          <van-tag
            wx:elif="{{houseDetail.houseInfo.house_status === '2003'}}"
            type="primary"
            size="small"
          >已交房</van-tag>
          <van-tag
            wx:elif="{{houseDetail.houseInfo.house_status === '2004'}}"
            type="warning"
            size="small"
          >未入住</van-tag>
          <van-tag
            wx:else
            type="default"
            size="small"
          >{{houseDetail.houseInfo.house_status || '未知'}}</van-tag>
        </view>
      </view>
    </view>

    <!-- 业主信息 -->
    <view class="info-card">
      <view class="card-header">
        <van-icon name="contact" size="20px" color="#52c41a" />
        <text class="card-title">业主信息 ({{houseDetail.ownerList.length}}位)</text>
      </view>

      <view wx:if="{{houseDetail.ownerList.length > 0}}" class="owner-simple-list">
        <view
          wx:for="{{houseDetail.ownerList}}"
          wx:key="owner_id"
          class="owner-simple-item"
          bindtap="callOwner"
          data-mobile="{{item.mobile}}"
        >
          <view class="owner-simple-info">
            <view class="owner-left">
              <view class="owner-simple-name">
                {{item.owner_name}}
                <van-tag
                  wx:if="{{item.is_default === 1}}"
                  type="primary"
                  size="small"
                >默认</van-tag>
                <view class="owner-simple-role">{{utils.getRelTypeText(item.rel_type)}}</view>
              </view>
            </view>
            <view class="owner-simple-mobile">{{item.mobile}}</view>
          </view>
          <van-icon name="phone-o" size="20px" color="#1989fa" />
        </view>
      </view>

      <view class="empty-state" wx:else>
        <van-empty description="暂无绑定业主" />
      </view>
    </view>

    <!-- 车位信息 -->
    <view class="info-card" wx:if="{{houseDetail.parkingList.length > 0}}">
      <view class="card-header">
        <van-icon name="location-o" size="20px" color="#ff7875" />
        <text class="card-title">车位信息 ({{houseDetail.parkingList.length}}个)</text>
      </view>

      <view class="parking-list">
        <view
          wx:for="{{houseDetail.parkingList}}"
          wx:key="space_id"
          class="parking-item"
        >
          <view class="parking-main">
            <view class="parking-no">{{item.space_no}}</view>
            <view class="parking-info">
              <text class="parking-type">{{utils.getParkingTypeText(item.space_type)}}</text>
              <van-tag
                wx:if="{{item.space_status === '1'}}"
                type="success"
                size="small"
              >已租用</van-tag>
              <van-tag
                wx:elif="{{item.space_status === '0'}}"
                type="default"
                size="small"
              >空闲</van-tag>
            </view>
          </view>
          <view class="parking-fee">
            <text class="fee-label">月租费：</text>
            <text class="fee-value">¥{{item.monthly_fee || 0}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 车辆信息 -->
    <view class="info-card" wx:if="{{houseDetail.vehicleList.length > 0}}">
      <view class="card-header">
        <van-icon name="logistics" size="20px" color="#722ed1" />
        <text class="card-title">车辆信息 ({{houseDetail.vehicleList.length}}辆)</text>
      </view>

      <view class="vehicle-list">
        <view
          wx:for="{{houseDetail.vehicleList}}"
          wx:key="vehicle_id"
          class="vehicle-item"
        >
          <view class="vehicle-main">
            <view class="vehicle-plate">{{item.plate_number}}</view>
            <view class="vehicle-owner">车主：{{item.owner_name || '未知'}}</view>
          </view>
          <view class="vehicle-detail">
            <view class="detail-row">
              <text class="detail-label">车辆类型：</text>
              <text class="detail-value">{{getVehicleTypeText(item.vehicle_type)}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">品牌型号：</text>
              <text class="detail-value">{{item.vehicle_brand}} {{item.vehicle_model}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">车辆颜色：</text>
              <text class="detail-value">{{item.vehicle_color || '未知'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">登记时间：</text>
              <text class="detail-value">{{formatTime(item.create_time)}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{error}}">
    <van-empty description="{{error}}" />
  </view>
</view>
