// 业主管理页面
import { handlePropertyPageShow } from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    loading: true,
    houseLoading: false,
    
    // 搜索相关
    searchKeyword: '',
    
    // 楼栋相关
    buildings: [],
    filteredBuildings: [],
    activeBuildingIndex: 0,
    currentBuilding: null,
    
    // 房屋相关
    houses: [],
    housePageNum: 1,
    housePageSize: 20,
    houseHasMore: true,


  },

  onLoad() {
    console.log('[业主管理] 页面加载')

    // 验证用户权限
    this.checkUserPermission()
  },

  onShow() {
    // 设置物业TabBar
    handlePropertyPageShow(this, () => {
      this.loadBuildings()
    })
  },

  onReady() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '业主管理'
    })
  },

  /**
   * 验证用户权限
   */
  checkUserPermission() {
    const tokenUser = app.globalData.tokenUser
    if (!tokenUser || tokenUser.userType !== '2') {
      wx.showModal({
        title: '权限不足',
        content: '此功能仅限物业用户使用',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      })
      return false
    }
    return true
  },

  /**
   * 加载楼栋列表
   */
  async loadBuildings() {
    if (!this.checkUserPermission()) return

    try {
      this.setData({ loading: true })

      const response = await app.request({
        url: '/api/wx/owner/buildings',
        method: 'GET'
      })

      if (response.code === 0) {
        const buildings = response.data || []
        this.setData({
          buildings: buildings,
          filteredBuildings: buildings,
          loading: false
        })

        // 如果有楼栋，默认选择第一个
        if (buildings.length > 0) {
          this.setData({
            currentBuilding: buildings[0],
            activeBuildingIndex: 0
          })
          this.loadHouses(buildings[0].buildingId)
        }
      } else {
        throw new Error(response.msg || '获取楼栋列表失败')
      }
    } catch (error) {
      console.error('[业主管理] 加载楼栋列表失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载房屋列表
   */
  async loadHouses(buildingId, isRefresh = true) {
    if (!buildingId) return

    try {
      if (isRefresh) {
        this.setData({
          houseLoading: true,
          housePageNum: 1,
          houses: [],
          houseHasMore: true
        })
      }

      const { housePageNum, housePageSize } = this.data

      const response = await app.request({
        url: `/api/wx/owner/houses/${buildingId}`,
        method: 'GET',
        data: {
          pageNum: housePageNum,
          pageSize: housePageSize
        }
      })

      if (response.code === 0) {
        const newHouses = response.data || []
        const hasMore = newHouses.length === housePageSize

        this.setData({
          houses: isRefresh ? newHouses : [...this.data.houses, ...newHouses],
          houseLoading: false,
          houseHasMore: hasMore,
          housePageNum: housePageNum + 1
        })
      } else {
        throw new Error(response.msg || '获取房屋列表失败')
      }
    } catch (error) {
      console.error('[业主管理] 加载房屋列表失败:', error)
      this.setData({ houseLoading: false })
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载更多房屋
   */
  async loadMoreHouses() {
    const { currentBuilding, houseHasMore, houseLoading } = this.data

    if (!currentBuilding || !houseHasMore || houseLoading) {
      return
    }

    await this.loadHouses(currentBuilding.buildingId, false)
  },

  /**
   * 楼栋切换事件
   */
  onBuildingChange(event) {
    const index = event.detail
    const building = this.data.filteredBuildings[index]
    
    if (building) {
      this.setData({
        activeBuildingIndex: index,
        currentBuilding: building
      })
      this.loadHouses(building.buildingId, true)
    }
  },

  /**
   * 房屋点击事件
   */
  onHouseClick(event) {
    const house = event.currentTarget.dataset.house
    if (house) {
      wx.navigateTo({
        url: `/pages/property/owners/detail?houseId=${house.houseId}`
      })
    }
  },



  /**
   * 搜索输入变化
   */
  onSearchChange(event) {
    const keyword = event.detail.trim()
    this.setData({ searchKeyword: keyword })
    
    // 实时过滤楼栋列表
    this.filterBuildings(keyword)
  },

  /**
   * 搜索提交
   */
  onSearchSubmit() {
    const keyword = this.data.searchKeyword.trim()
    if (keyword) {
      this.searchBuildings(keyword)
    } else {
      this.filterBuildings('')
    }
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({ searchKeyword: '' })
    this.filterBuildings('')
  },

  /**
   * 本地过滤楼栋列表
   */
  filterBuildings(keyword) {
    const { buildings } = this.data
    
    if (!keyword) {
      this.setData({
        filteredBuildings: buildings,
        activeBuildingIndex: 0
      })
      
      if (buildings.length > 0) {
        this.setData({ currentBuilding: buildings[0] })
        this.loadHouses(buildings[0].buildingId)
      }
      return
    }

    const filtered = buildings.filter(building => 
      building.name.includes(keyword)
    )
    
    this.setData({
      filteredBuildings: filtered,
      activeBuildingIndex: 0,
      currentBuilding: filtered[0] || null,
      houses: []
    })

    if (filtered.length > 0) {
      this.loadHouses(filtered[0].buildingId)
    }
  },

  /**
   * 服务端搜索楼栋
   */
  async searchBuildings(keyword) {
    try {
      const response = await app.request({
        url: '/api/wx/owner/searchBuildings',
        method: 'GET',
        data: { keyword }
      })

      if (response.code === 0) {
        const buildings = response.data || []
        this.setData({
          filteredBuildings: buildings,
          activeBuildingIndex: 0,
          currentBuilding: buildings[0] || null,
          houses: []
        })

        if (buildings.length > 0) {
          this.loadHouses(buildings[0].buildingId)
        }
      } else {
        throw new Error(response.msg || '搜索失败')
      }
    } catch (error) {
      console.error('[业主管理] 搜索楼栋失败:', error)
      wx.showToast({
        title: error.message || '搜索失败',
        icon: 'none'
      })
    }
  },


})
